import { Box, Center } from "@hope-ui/solid";
import { raceTimeout } from "@solid-primitives/promise";
import { Title } from "@solidjs/meta";
import { RouteDefinition, useNavigate, useSearchParams } from "@solidjs/router";
import isString from "lodash-es/isString";
import { createSignal, JSX, onCleanup, onMount } from "solid-js";
import { useService } from "solid-services";
import { until } from "solidjs-use";
import { getMemberSessionInfo, onSessionRestore } from "~/lib";
import { WelcomeDto } from "~/proto/client-message";
import { getWebsocketIdentity } from "~/server/general.api";
import AppService from "~/services/app.service";
import AudioService from "~/services/audio.service";
import ChatService from "~/services/chat.service";
import CoreWasmService from "~/services/core-wasm.service";
import DisplaysService from "~/services/displays.service";
import I18nService from "~/services/i18n.service";
import InitializationService from "~/services/initialization.service";
import KeyboardService from "~/services/keyboard.service";
import MonitorTrackingService from "~/services/monitor-tracking.service";
import NotificationService from "~/services/notification.service";
import PlatformService from "~/services/platform.service";
import ResourceService from "~/services/resource.service";
import RoomsService from "~/services/rooms.service";
import { SelfHostingService } from "~/services/selfhosting.service";
import AppSettingsService from "~/services/settings-storage.service";
import SoundEffectsService from "~/services/sound-effects.service";
import { TauriService } from "~/services/tauri.service";
import UsersService from "~/services/users-service";
import WebMidiService from "~/services/webmidi.service";
import WebsocketService from "~/services/websocket.service";
import { CurrentPage } from "~/types/app.types";
import { DEFAULT_SOUNDFONT, SoundfontSetting } from "~/types/audio.types";
import { InitializationStep, InitializationStatus } from "~/types/initialization.types";
import { AppSettings as AppSettingsType, DefaultAppSettings } from "~/types/settings.types";
import { UserDtoUtil } from "~/types/user-helper";
import { COMMON, ERROR_MESSAGES, IDS } from "~/util/const.common";
import { userGestures } from "~/util/helpers";
import { logDebug, logError, logInfo } from "~/util/logger";
import SwalPR from "~/util/sweetalert";
import { SynthAudioContext } from "~/util/synth-audio-ctx";
import { parseZipzon } from "~/util/zipzon.util";
import { AppStateEvents } from "~/proto/pianorhythm-events";

export const route = {
  async preload(props) {
    // Ensures the appropriate tokens and session information are loaded
    if (props.intent == "initial") {
      await onSessionRestore();
    } else {
      await getMemberSessionInfo();
    }
  }
} satisfies RouteDefinition;

const DEFAULT_SERVICE_TIMEOUT = 30_000;

/**
 * Component responsible for loading the application and initializing various services.
 * It handles user gestures, loads settings, soundfonts, and initializes services like WebMidi, keyboard, display, and resource services.
 * It also connects to the server via a websocket and navigates to the lobby if already connected.
 *
 * @returns {JSX.Element} The rendered AppLoading component.
 */
export default function AppLoading(): JSX.Element {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const appService = useService(AppService);
  const websocketService = useService(WebsocketService);
  const audioService = useService(AudioService);
  const sfxService = useService(SoundEffectsService);
  const keyboardService = useService(KeyboardService);
  const displayService = useService(DisplaysService);
  const webmidiService = useService(WebMidiService);
  const resourceService = useService(ResourceService);
  const appSettingsService = useService(AppSettingsService);
  const monitorService = useService(MonitorTrackingService);
  const platformService = useService(PlatformService);
  const usersService = useService(UsersService);
  const chatService = useService(ChatService);
  const roomsService = useService(RoomsService);
  const selfHostingService = useService(SelfHostingService);
  const i18nService = useService(I18nService);
  const initializationService = useService(InitializationService);

  const [progressText, _setProgressText] = createSignal("Loading application...");
  const triggerInitializationAbortController = new AbortController();

  let initializing = false;

  onMount(async () => {
    if (appSettingsService().isDebugMode()) console.log("AppLoading mounted... | Room Name:", searchParams.roomName);
    appService().setActivatePageLoader(true);
    NotificationService.hide(IDS.USER_LOGGING_IN);
    appService().setCurrentPage(CurrentPage.AppLoading);
    await triggerInitialization();
  });

  onCleanup(() => {
    if (appSettingsService().isDebugMode()) console.log("AppLoading unmounted...");
    triggerInitializationAbortController.abort();
    appService().userGestureController.abort();
    NotificationService.hide(IDS.WAIT_FOR_GESTURE_ACTION);
    appService().userGestureController = new AbortController();
  });

  const onDisconnect = () => {
    appService().onDisconnect();
    websocketService().disconnect();
    navigate("/login", { replace: true });
  };

  const setProgressText = (text: string, type?: string) => {
    NotificationService.show({
      id: IDS.APP_LOADING,
      title: "Initializing",
      description: text,
      type: type as any ?? "info"
    });
    appService().setActivePageLoaderToolTip(text);
    _setProgressText(text);
    console.debug("[App Loading]", text);
  };

  const onClientLoadSettings = async (settings?: AppSettingsType) => {
    let _appSettingsService = appSettingsService();

    if (settings) {
      _appSettingsService.clearSettings();
      _appSettingsService.loadSettingsFromServer(settings);
    } else {
      _appSettingsService.loadGuestSettings();
    }

    const targetVersion = "0.8.24";

    // One-off migration to update default reverb values
    if (COMMON.CLIENT_VERSION == targetVersion
      && !_appSettingsService.getSetting("AUDIO_REVERB_DEFAULT_MIGRATED_VERSION")
    ) {
      if (
        _appSettingsService.getSetting("AUDIO_REVERB_LEVEL") == 0.9 &&
        _appSettingsService.getSetting("AUDIO_REVERB_DAMP") == 0.15 &&
        _appSettingsService.getSetting("AUDIO_REVERB_WIDTH") == 0.5 &&
        _appSettingsService.getSetting("AUDIO_REVERB_ROOMSIZE") == 0.2
      ) {
        _appSettingsService.saveSetting("AUDIO_REVERB_LEVEL", DefaultAppSettings.AUDIO_REVERB_LEVEL);
        _appSettingsService.saveSetting("AUDIO_REVERB_DAMP", DefaultAppSettings.AUDIO_REVERB_DAMP);
        _appSettingsService.saveSetting("AUDIO_REVERB_WIDTH", DefaultAppSettings.AUDIO_REVERB_WIDTH);
        _appSettingsService.saveSetting("AUDIO_REVERB_ROOMSIZE", DefaultAppSettings.AUDIO_REVERB_ROOMSIZE);
        _appSettingsService.saveSetting("AUDIO_REVERB_DEFAULT_MIGRATED_VERSION", targetVersion);
      }
    }

    _appSettingsService.persistSettings();
    _appSettingsService.setLocalStorage("useOffscreenCanvas", appSettingsService().getSetting("GRAPHICS_USE_OFFSCREEN_CANVAS"));

    audioService().onLoadSettings();
  };

  const onLoadClientSoundfont = async () => {
    try {
      let sf = appSettingsService().getSetting<SoundfontSetting | string | null>("SELECTED_SOUNDFONT");
      if (!sf) return undefined;

      let output = "";
      let isCustom = false;

      if (isString(sf)) {
        output = sf;
      } else {
        output = sf.path || sf.name;
        isCustom = sf.custom || false;

        // [Migration] Update default soundfont
        if (output && output.toLowerCase() == "PR_GM2.sf2".toLowerCase() && !isCustom) {
          output = DEFAULT_SOUNDFONT;
        }
      }

      await audioService().loadSoundfont(output, isCustom, true);
      return output;
    } catch {
    }

    return undefined;
  };

  const onWelcomeEvent = async (welcomeDto: WelcomeDto) => {
    let dto = welcomeDto.userClientDto!;
    let settings = welcomeDto.settings;
    let userDto = dto.userDto!;
    let userDtoRoles = userDto.roles ?? [];
    let isMember = UserDtoUtil.isMember(userDtoRoles ?? []);

    if (settings && isMember) {
      try {
        let parsedSettings = parseZipzon<AppSettingsType>(settings);
        if (!parsedSettings) throw new Error("Failed to parse client settings...");
        await onClientLoadSettings({ ...DefaultAppSettings, ...parsedSettings });
      } catch (ex) {
        await SwalPR(sfxService).fire({
          icon: "warning",
          title: "Failed to load settings",
          html: `Sorry! Something went wrong when trying to load your account settings. <br/><br/> Would you like to load the default?`,
          showCancelButton: true,
          cancelButtonText: "Exit"
        }).then(async (result) => {
          if (result.isConfirmed) {
            await onClientLoadSettings();
            logInfo("Loaded default settings...");
          } else {
            // exiting = true;
            throw new Error("Failed to load client settings...");
          }
        });
      }
    } else {
      await onClientLoadSettings();
    }
    setProgressText("Loaded client settings...");

    appSettingsService().setIsClientGuest(!isMember);

    if (UserDtoUtil.isGuest(userDtoRoles)) {
      appSettingsService().setLocalStorage("lastSavedUsername", userDto.username);
    } else if (UserDtoUtil.isMember(userDtoRoles)) {
      appSettingsService().setLocalStorage("lastSavedLoggedInUsername", userDto.username);
    }
  };

  const triggerInitialization = async () => {
    if (initializing) return;
    initializing = true;
    if (appSettingsService().isDebugMode()) console.log("Triggering initialization...");

    // Setup initialization service callbacks
    initializationService().setProgressCallback((step: string, status: InitializationStatus, progress: number) => {
      const stepName = step.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
      let message = `${stepName}...`;

      if (appSettingsService().isDebugMode())
        console.log("Step Name", stepName, "| Status:", status, "| Progress:", progress);

      if (status === InitializationStatus.InProgress) {
        message = `${stepName} in progress...`;
      } else if (status === InitializationStatus.Completed) {
        message = `${stepName} completed.`;
      } else if (status === InitializationStatus.Retrying) {
        message = `${stepName} retrying...`;
      }

      setProgressText(`${message} (${progress}%)`);
    });

    initializationService().setErrorCallback((step, error) => {
      logError(`[${step}] ${error}`);
      setProgressText(`Failed at ${step}: ${error}`, "danger");
    });

    const appLoadFailed = (ex?: any) => {
      if (ex) {
        logError(ex);
      } else {
        logError("Failed to initialize application.");
      }
      initializing = false;
      setProgressText(`Failed to initialize application. Please try again later.: ${ex}`, "danger");
      onDisconnect();
    };

    try {
      // Reset initialization state
      initializationService().reset();

      // Execute initialization steps in order
      await executeInitializationSteps();

      setProgressText(`Initialization complete! Joining room...`);
      appService().setInitialized(true);
      appService().userGestureController = new AbortController();
      initializing = false;

      let targetRoomName = `${searchParams.roomName ?? "lobby"}`;
      window.setTimeout(() => {
        NotificationService.hide(IDS.APP_LOADING);
        navigate(`/room/${targetRoomName}`, { replace: true });
      }, 1000);

    } catch (ex) {
      appLoadFailed(ex);
    }
  };

  const executeInitializationSteps = async () => {
    // Step 1: User Gesture
    await initializationService().executeStep(InitializationStep.UserGesture, {
      execute: async () => {
        i18nService().initialize(appService().appStateEffects);

        if (SynthAudioContext.Instance.context?.state != "running") {
          let clickMessage = "Please click anywhere to get started!";

          appService().setActivePageLoaderToolTip(clickMessage);
          _setProgressText(clickMessage);

          NotificationService.show({
            id: IDS.WAIT_FOR_GESTURE_ACTION,
            title: "Initializing",
            description: clickMessage,
            duration: Infinity,
            type: "info"
          });

          logDebug("Waiting for user gesture...");
          let _continue = await Promise.race(userGestures())
            .then(() => {
              if (appService().userGestureController.signal.aborted) return false;
              return !triggerInitializationAbortController.signal.aborted;
            });

          if (!_continue) {
            throw new Error("User gesture aborted.");
          }

          logDebug("User gesture received...");
        }

        if (!appService().initialized() && appService().userGestureController.signal.aborted) {
          appService().userGestureController = new AbortController();
          throw new Error("User gesture aborted during initialization.");
        }

        NotificationService.hide(IDS.WAIT_FOR_GESTURE_ACTION);
        NotificationService.show({
          id: IDS.APP_LOADING,
          title: "Initializing",
          description: "Please wait...",
          type: "info"
        });
      }
    });

    // Step 2: Core WASM
    await initializationService().executeStep(InitializationStep.CoreWasm, {
      execute: async () => {
        if (!appService().coreService()) {
          await appService().loadCoreWasm(new CoreWasmService(), new TauriService());
        }
      },
      validate: async () => !!appService().coreService()
    });

    // Step 3: App State
    let appStateEventsSubscription: VoidFunction | null = null;
    await initializationService().executeStep(InitializationStep.AppState, {
      execute: async () => {
        appService().sendInitializeAppState();

        await new Promise((resolve, reject) => {
          appStateEventsSubscription = appService().appStateEvents.listen((event => {
            if (event == AppStateEvents.ClientLoggedIn) {
              resolve(true);
            }
          }))
        })
      },
      cleanup: async () => {
        appStateEventsSubscription?.();
      }
    });

    // Initialize other services that don't depend on websocket
    await initializeBasicServices();

    // Step 4: Websocket Identity
    let wsIdentityResponse: { identity: string; } | null = null;
    await initializationService().executeStep(InitializationStep.WebsocketIdentity, {
      execute: async () => {
        // TODO: What actually happens if we are already connected?
        if (websocketService().connected()) {
          navigate("/room/lobby");
          return;
        }

        try {
          wsIdentityResponse = await getWebsocketIdentity();
        } catch (ex) {
          logError("Failed to get websocket identity...");
          NotificationService.show({
            title: "Failed to connect to server",
            description: `${ex} - Please try again later...`,
            type: "danger",
            duration: 5000
          });

          let path = "/login";
          if (searchParams.roomName) path += `?roomName=${searchParams.roomName}`;
          navigate(path, { replace: true });
          throw ex;
        }
      }
    });

    // Step 5: Websocket Connection
    await initializationService().executeStep(InitializationStep.WebsocketConnection, {
      execute: async () => {
        websocketService().initialize();

        let websocketEventsSub = websocketService().websocketEvents.listen((event) => {
          if (event != "connected") {
            websocketEventsSub();
            throw new Error("Failed to connect to server.");
          }
        });

        await new Promise<void>((resolve, reject) => {
          websocketService().connect(wsIdentityResponse!.identity, async () => {
            try {
              websocketEventsSub();
              resolve();
            } catch (ex) {
              reject(ex);
            }
          });
        });

        await raceTimeout(until(websocketService().connected), DEFAULT_SERVICE_TIMEOUT * 2, true, "Websocket connection failed to establish.");
      }
    });

    // Continue with remaining steps...
    await executePostConnectionSteps();
  };

  const initializeBasicServices = async () => {
    if (appSettingsService().isDebugMode()) {
      (window as any).keyboardService = keyboardService();
      (window as any).displayService = displayService();
    }

    // Initialize basic services
    sfxService().initialize();
    keyboardService().initialize();
    displayService().initialize();
    await resourceService().initialize();

    if (!platformService().initialized()) {
      platformService().initialize(sfxService);
      let isMobile = platformService().isMobile();
      appService().sendIsMobileAppAction(isMobile);
    }

    // Initialize WebMidi (can fail gracefully)
    const [webMidiNotSupported, setWebMidiNotSupported] = createSignal(false);
    webmidiService().initialize()
      .then(permission => {
        if (!permission) setWebMidiNotSupported(true);
      })
      .catch((err) => {
        if (err == ERROR_MESSAGES.WebMidiNotSupported) {
          NotificationService.show({
            title: "WebMidi not supported",
            description: "Sorry, your browser does not support WebMidi. Please use Chrome or Edge.",
            type: "danger",
            duration: 5000
          });
          setWebMidiNotSupported(true);
        }
      });

    await raceTimeout(
      until(() => webmidiService().initialized() || webMidiNotSupported()),
      DEFAULT_SERVICE_TIMEOUT * 10, true, "Web Midi service never initialized."
    );
  };

  const executePostConnectionSteps = async () => {
    // Step 8: Welcome Event (now executes after AudioService and SynthEngine)
    await initializationService().executeStep(InitializationStep.WelcomeEventReceieved, {
      execute: async () => {
        await raceTimeout(until(appService().welcomeEvent), DEFAULT_SERVICE_TIMEOUT, true, "Welcome event never received.");
      },
      validate: async () => !!appService().welcomeEvent()
    });

    // Step 10: Client Socket ID (ensure socket ID is available)
    await initializationService().executeStep(InitializationStep.ClientSocketId, {
      execute: async () => {
        // Wait for client socket ID to be available
        await raceTimeout(until(() => {
          const socketId = appService().getSocketID();
          return !!socketId;
        }), DEFAULT_SERVICE_TIMEOUT, true, "Client socket ID never became available.");
      },
      validate: async () => {
        return !!appService().getSocketID();
      }
    });

    // Step 9: Client Loaded
    await initializationService().executeStep(InitializationStep.HandleOnWelcomeEvent, {
      execute: async () => {
        await onWelcomeEvent(appService().welcomeEvent()!);
      },
      validate: async () => appService().clientLoaded()
    });

    // Step 6: Audio Service (now executes before WelcomeEvent)
    await initializationService().executeStep(InitializationStep.AudioService, {
      execute: async () => {
        if (!audioService().initialized()) {
          await audioService().initialize();
          await raceTimeout(until(audioService().initialized), DEFAULT_SERVICE_TIMEOUT, true, "Audio service never initialized.");
        }
      }
    });

    // Step 7: Synth Engine (created during audio service initialization)
    await initializationService().executeStep(InitializationStep.SynthEngine, {
      execute: async () => {
        // The synth engine is created during audio service initialization
        // We just need to verify it exists
        if (!appService().coreService()) {
          throw new Error("Core service not available for synth engine");
        }
      },
      validate: async () => {
        return !!appService().coreService() && audioService().initialized();
      }
    });

    // Step 11: Dispatch SynthAction to add client to synth engine
    let shouldDispatchAddClient = () => {
      return appService().clientLoaded() &&
        !!appService().getSocketID() &&
        audioService().initialized() &&
        !!appService().coreService();
    };

    await initializationService().executeStep(InitializationStep.SynthActionDispatch, {
      execute: async () => {
        if (shouldDispatchAddClient()) {
          logInfo("[SynthActionDispatch] Skipping dispatch as all conditions are already met.");
          return;
        }

        // Dispatch the SynthAction to add client to synth engine
        await audioService().dispatchAddClientSynthAction();
      },
      validate: async () => shouldDispatchAddClient()
    });

    // Step 12: Client Added to Synth (wait for the AddedClientSynthUser event)
    await initializationService().executeStep(InitializationStep.ClientAddedToSynth, {
      execute: async () => {
        // Wait for the AddedClientSynthUser event to be received
        await raceTimeout(until(() => {
          return audioService().clientAdded();
        }), DEFAULT_SERVICE_TIMEOUT, true, "Client never properly added to synth - AddedClientSynthUser event not received.");
      },
      validate: async () => {
        return audioService().clientAdded();
      }
    });

    // Step 13: Load Soundfont (now happens after client is added to synth)
    await initializationService().executeStep(InitializationStep.Soundfont, {
      execute: async () => {
        let soundfontLoaded = await onLoadClientSoundfont();

        if (!soundfontLoaded) {
          let sf = appSettingsService().getSetting<SoundfontSetting | string | null>("SELECTED_SOUNDFONT");
          let sfName = sf && typeof sf == "object" ? sf.name : sf;

          // Try to load default soundfont on any failures
          NotificationService.show({
            id: "default-sf-load",
            type: "warning",
            duration: 10_000,
            title: "Soundfont Load - Fail",
            closable: true,
            description: `Sorry, something went wrong trying to load the soundfont '${sfName ?? ""}' that was in your settings.
            \nWill now attempt to load the default one.`
          });

          if (soundfontLoaded != DEFAULT_SOUNDFONT) {
            logError(`Failed to load client soundfont. Now attempting to load default. Sample Rate: ${audioService().sampleRate()}`);
            try {
              await audioService().loadSoundfont(DEFAULT_SOUNDFONT);
              // Save in settings if successful
              appSettingsService().saveSetting("SELECTED_SOUNDFONT", DEFAULT_SOUNDFONT);
            } catch (ex) {
              NotificationService.show({
                id: "default-sf-load-fail",
                type: "danger",
                duration: 10_000,
                title: "Default Soundfont Load Fail",
                closable: true,
                description: `Sorry, something went wrong trying to load the default soundfont.`
              });
              throw ex;
            }
          }
        }
      },
      validate: async () => {
        // Validate that a soundfont is loaded
        return !!audioService().loadedSoundfontName();
      }
    });

    // Step 13: App Settings (now depends on soundfont being loaded)
    await initializationService().executeStep(InitializationStep.AppSettings, {
      execute: async () => {
        await appSettingsService().initialize(appService().coreService()!, appService().appStateEffects);
      }
    });

    // Step 14: Users Service
    await initializationService().executeStep(InitializationStep.UsersService, {
      execute: async () => {
        await usersService().initialize(
          appService().coreService()!,
          appService().appStateEffects
        );
      }
    });

    // Step 15: Chat Service
    await initializationService().executeStep(InitializationStep.ChatService, {
      execute: async () => {
        chatService().initialize(
          appService().appStateEffects,
          appService().onDisconnectEvents
        );
      }
    });

    // Step 16: Rooms Service
    await initializationService().executeStep(InitializationStep.RoomsService, {
      execute: async () => {
        roomsService().initialize(
          appService().coreService()!,
          appService().appStateEffects,
          appService().appStateEvents
        );
      }
    });

    // Step 17: Monitor Service
    await initializationService().executeStep(InitializationStep.MonitorService, {
      execute: async () => {
        monitorService().initialize();
        selfHostingService().initialize(appService().appStateEvents, appService().appStateEffects);
      }
    });

    // Load webmidi state if permission granted
    if (webmidiService().hasMidiPermission()) {
      await webmidiService().refreshMidiState(true);
    }

    // Step 18: Mark as complete
    await initializationService().executeStep(InitializationStep.Complete, {
      execute: async () => {
        // Final validation that everything is ready
      }
    });
  };

  return (<>
    <Title>PianoRhythm - Loading App</Title>
    <Center w="100vw" h="100dvh">
      <Box>{progressText()}</Box>
    </Center>
  </>);
};